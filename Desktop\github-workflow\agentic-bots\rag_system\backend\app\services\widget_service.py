"""
Service for managing embeddable chat widgets.
"""
from typing import List, Optional, Dict, Any
import uuid
import secrets
from datetime import datetime
from urllib.parse import urlparse

from app.models.schemas import (
    ChatWidget, ChatWidgetCreate, ChatWidgetUpdate,
    PaginatedResponse
)
from app.database import get_supabase_client
import structlog

logger = structlog.get_logger()


class WidgetService:
    """Service for managing embeddable chat widgets."""
    
    # ... (create_widget, get_widget, get_widget_by_api_key, etc. remain the same) ...
    # PASTE THE EXISTING METHODS FROM YOUR FILE HERE, up to _domain_matches
    async def create_widget(self, widget_data: ChatWidgetCreate) -> ChatWidget:
        """Create a new chat widget."""
        try:
            supabase_client = await get_supabase_client()
            
            widget_dict = widget_data.model_dump()
            widget_dict["id"] = str(uuid.uuid4())
            widget_dict["chatbot_id"] = str(widget_data.chatbot_id)
            widget_dict["api_key"] = self._generate_api_key()
            widget_dict["created_at"] = datetime.utcnow().isoformat()
            widget_dict["updated_at"] = datetime.utcnow().isoformat()
            
            response = supabase_client.table("chat_widgets").insert(widget_dict).execute()
            
            if not response.data:
                raise Exception("Failed to create widget")
            
            return ChatWidget(**response.data[0])
            
        except Exception as e:
            logger.error(f"Error creating widget: {e}")
            raise
    
    async def get_widget(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID) -> Optional[ChatWidget]:
        """Get a specific widget by ID."""
        try:
            supabase_client = await get_supabase_client()
            
            response = supabase_client.table("chat_widgets").select("*").eq("id", str(widget_id)).eq("chatbot_id", str(chatbot_id)).execute()
            
            if response.data:
                return ChatWidget(**response.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting widget: {e}")
            raise
    
    async def get_widget_by_api_key(self, api_key: str) -> Optional[ChatWidget]:
        """Get a widget by its API key."""
        try:
            supabase_client = await get_supabase_client()
            
            response = supabase_client.table("chat_widgets").select("*").eq("api_key", api_key).eq("is_active", True).execute()
            
            if response.data:
                return ChatWidget(**response.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting widget by API key: {e}")
            raise
    
    async def get_chatbot_widgets(
        self, 
        chatbot_id: uuid.UUID, 
        skip: int = 0, 
        limit: int = 10,
        active_only: bool = True
    ) -> PaginatedResponse:
        """Get all widgets for a chatbot with pagination."""
        try:
            supabase_client = await get_supabase_client()
            
            # Build query
            query = supabase_client.table("chat_widgets").select("*", count="exact").eq("chatbot_id", str(chatbot_id))
            
            if active_only:
                query = query.eq("is_active", True)
            
            # Get total count
            count_response = query.execute()
            total = len(count_response.data) if count_response.data else 0
            
            # Get paginated results
            query = query.range(skip, skip + limit - 1).order("created_at", desc=True)
            response = query.execute()
            
            widgets = [ChatWidget(**widget) for widget in response.data] if response.data else []
            
            return PaginatedResponse(
                items=widgets,
                total=total,
                page=skip // limit + 1,
                per_page=limit,
                pages=(total + limit - 1) // limit
            )
            
        except Exception as e:
            logger.error(f"Error getting chatbot widgets: {e}")
            raise
    
    async def update_widget(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID, widget_data: ChatWidgetUpdate) -> Optional[ChatWidget]:
        """Update an existing widget."""
        try:
            supabase_client = await get_supabase_client()
            
            # Only update fields that are provided
            update_dict = {k: v for k, v in widget_data.model_dump().items() if v is not None}
            
            if not update_dict:
                # If no fields to update, return current widget
                return await self.get_widget(widget_id, chatbot_id)
            
            update_dict["updated_at"] = datetime.utcnow().isoformat()
            
            response = supabase_client.table("chat_widgets").update(update_dict).eq("id", str(widget_id)).eq("chatbot_id", str(chatbot_id)).execute()
            
            if response.data:
                return ChatWidget(**response.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error updating widget: {e}")
            raise
    
    async def delete_widget(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID) -> bool:
        """Delete a widget (soft delete by setting is_active=False)."""
        try:
            supabase_client = await get_supabase_client()
            
            response = supabase_client.table("chat_widgets").update({
                "is_active": False,
                "updated_at": datetime.utcnow().isoformat()
            }).eq("id", str(widget_id)).eq("chatbot_id", str(chatbot_id)).execute()
            
            return bool(response.data)
            
        except Exception as e:
            logger.error(f"Error deleting widget: {e}")
            raise
    
    async def regenerate_api_key(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID) -> Optional[ChatWidget]:
        """Regenerate API key for a widget."""
        try:
            supabase_client = await get_supabase_client()
            
            new_api_key = self._generate_api_key()
            
            response = supabase_client.table("chat_widgets").update({
                "api_key": new_api_key,
                "updated_at": datetime.utcnow().isoformat()
            }).eq("id", str(widget_id)).eq("chatbot_id", str(chatbot_id)).execute()
            
            if response.data:
                return ChatWidget(**response.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error regenerating API key: {e}")
            raise
    
    async def validate_widget_access(self, api_key: str, origin: Optional[str]) -> tuple[bool, Optional[ChatWidget]]:
        """Validate if a request from a specific origin is allowed for the widget."""
        try:
            widget = await self.get_widget_by_api_key(api_key)
            if not widget:
                return False, None
            
            if not widget.is_active:
                return False, widget
            
            if not widget.allowed_domains:
                return True, widget
            
            if not origin:
                return False, widget

            try:
                parsed_origin = urlparse(origin)
                origin_domain = parsed_origin.netloc.lower()
            except Exception:
                return False, widget
            
            allowed = any(
                self._domain_matches(origin_domain, allowed_domain.strip().lower())
                for allowed_domain in widget.allowed_domains
            )
            
            return allowed, widget
            
        except Exception as e:
            logger.error(f"Error validating widget access: {e}")
            return False, None

    def _domain_matches(self, origin_domain: str, allowed_domain: str) -> bool:
        """Check if origin domain matches allowed domain (supports wildcards)."""
        if allowed_domain == "*":
            return True
        if origin_domain == allowed_domain:
            return True
        if allowed_domain.startswith("*."):
            base_domain = allowed_domain[2:]
            return origin_domain.endswith(f".{base_domain}") or origin_domain == base_domain
        return False
    
    def _generate_api_key(self) -> str:
        """Generate a secure API key for the widget."""
        return f"widget_{secrets.token_urlsafe(32)}"

    def _generate_api_key(self) -> str:
        """Generate a secure API key for the widget."""
        return f"widget_{secrets.token_urlsafe(32)}"

    async def generate_widget_script(self, api_key: str) -> Optional[str]:
        """Generates a simple JavaScript widget for the chatbot."""
        widget = await self.get_widget_by_api_key(api_key)
        if not widget:
            return "console.error('AI Chat Widget: Invalid API key or widget is inactive.');"

        # Get basic configuration
        config = widget.widget_config or {}
        appearance = config.get("appearance", {})
        primary_color = appearance.get("primaryColor", "#3B82F6")

        base_url = "http://localhost:8000"

        return f"""
(function() {{
    'use strict';

    // Prevent multiple instances
    if (window.AIChatWidget) return;

    // Create widget container
    const widgetContainer = document.createElement('div');
    widgetContainer.id = 'ai-chat-widget-container';
    widgetContainer.innerHTML = `
        <style>
            #ai-chat-widget-container {{
                position: fixed;
                z-index: 999999;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }}

            .ai-chat-widget-button {{
                position: fixed;
                bottom: 20px;
                right: 20px;
                width: 60px;
                height: 60px;
                border-radius: 50%;
                background: {primary_color};
                border: none;
                cursor: pointer;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000000;
            }}

            .ai-chat-widget-button:hover {{
                transform: scale(1.1);
            }}

            .ai-chat-widget-icon {{
                width: 24px;
                height: 24px;
                fill: white;
            }}

            .ai-chat-widget-iframe {{
                position: fixed;
                bottom: 90px;
                right: 20px;
                width: 400px;
                height: 600px;
                border: none;
                border-radius: 12px;
                box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
                background: white;
                transform: scale(0.8);
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                z-index: 999999;
            }}

            .ai-chat-widget-iframe.open {{
                transform: scale(1);
                opacity: 1;
                visibility: visible;
            }}

            @media (max-width: 480px) {{
                .ai-chat-widget-iframe {{
                    width: calc(100vw - 20px);
                    height: calc(100vh - 100px);
                    bottom: 90px;
                    right: 10px;
                    left: 10px;
                }}
            }}
        </style>

        <button class="ai-chat-widget-button" id="ai-chat-button" aria-label="Open chat">
            <svg class="ai-chat-widget-icon" viewBox="0 0 24 24">
                <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
            </svg>
        </button>

        <iframe
            class="ai-chat-widget-iframe"
            id="ai-chat-iframe"
            src="{base_url}/widget/{widget.api_key}"
            title="AI Chat Assistant"
        ></iframe>
    `;

    // Simple widget functionality
    class AIChatWidget {{
        constructor() {{
            this.isOpen = false;
            this.button = document.getElementById('ai-chat-button');
            this.iframe = document.getElementById('ai-chat-iframe');

            this.button.addEventListener('click', () => this.toggle());
        }}

        toggle() {{
            this.isOpen = !this.isOpen;
            if (this.isOpen) {{
                this.iframe.classList.add('open');
            }} else {{
                this.iframe.classList.remove('open');
            }}
        }}
    }}

    // Initialize widget
    function initWidget() {{
        document.body.appendChild(widgetContainer);
        window.AIChatWidget = new AIChatWidget();
    }}

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {{
        document.addEventListener('DOMContentLoaded', initWidget);
    }} else {{
        initWidget();
    }}

}})();
"""

    async def get_widget_embed_code(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID) -> Dict[str, str]:
        """Generate simple embed codes for the widget."""
        try:
            widget = await self.get_widget(widget_id, chatbot_id)
            if not widget:
                raise ValueError(f"Widget {widget_id} not found")

            base_url = "http://localhost:8000"
            script_url = f"{base_url}/api/v1/widgets/script.js?key={widget.api_key}"

            # Simple Script Tag (Recommended)
            script_code = f"""<!-- AI Chatbot Widget -->
<script src="{script_url}" async defer></script>"""

            # Basic iframe embed
            iframe_code = f"""<!-- AI Chatbot Widget - iframe -->
<iframe
    src="{base_url}/widget/{widget.api_key}"
    width="400"
    height="600"
    frameborder="0"
    style="position: fixed; bottom: 20px; right: 20px; z-index: 9999; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.15);">
</iframe>"""

            # React component
            react_code = f"""// React Component for AI Chat Widget
import React, {{ useEffect }} from 'react';

const AIChatWidget = () => {{
    useEffect(() => {{
        const script = document.createElement('script');
        script.src = '{script_url}';
        script.async = true;
        script.defer = true;
        document.head.appendChild(script);

        return () => {{
            const existingScript = document.querySelector(`script[src="${{script.src}}"]`);
            if (existingScript) existingScript.remove();

            const widgetContainer = document.getElementById('ai-chat-widget-container');
            if (widgetContainer) widgetContainer.remove();

            if (window.AIChatWidget) delete window.AIChatWidget;
        }};
    }}, []);

    return null;
}};

export default AIChatWidget;"""

            return {
                "script_tag": script_code.strip(),
                "iframe_embed": iframe_code.strip(),
                "react_component": react_code.strip(),
                "widget_url": f"{base_url}/widget/{widget.api_key}",
                "script_url": script_url,
                "configuration": widget.widget_config
            }

        except Exception as e:
            logger.error(f"Error generating embed code: {e}")
            raise

    async def get_widget_analytics(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID, days: int = 30) -> Dict[str, Any]:
        """Get analytics data for a widget."""
        try:
            # This would typically query analytics data from a separate analytics service
            # For now, return mock data structure
            
            analytics = {
                "widget_id": str(widget_id),
                "period_days": days,
                "total_conversations": 0,
                "total_messages": 0,
                "average_session_duration": 0,
                "popular_questions": [],
                "daily_stats": [],
                "domain_stats": {},
                "action_executions": 0
            }
            
            # In a real implementation, you would:
            # 1. Query conversation logs
            # 2. Calculate metrics
            # 3. Return real analytics data
            
            return analytics
            
        except Exception as e:
            logger.error(f"Error getting widget analytics: {e}")
            raise
    
    async def customize_widget_appearance(
        self, 
        widget_id: uuid.UUID, 
        chatbot_id: uuid.UUID, 
        appearance_config: Dict[str, Any]
    ) -> Optional[ChatWidget]:
        """Update widget appearance configuration."""
        try:
            widget = await self.get_widget(widget_id, chatbot_id)
            if not widget:
                return None
            
            # Merge appearance config with existing widget config
            current_config = widget.widget_config.copy()
            current_config.update({
                "appearance": appearance_config,
                "updated_at": datetime.utcnow().isoformat()
            })
            
            update_data = ChatWidgetUpdate(widget_config=current_config)
            return await self.update_widget(widget_id, chatbot_id, update_data)
            
        except Exception as e:
            logger.error(f"Error customizing widget appearance: {e}")
            raise

    def get_default_widget_config(self) -> Dict[str, Any]:
        """Get default widget configuration."""
        return {
            "appearance": {
                "primaryColor": "#3B82F6",
                "position": "bottom-right",
                "width": "400px",
                "height": "600px"
            },
            "behavior": {
                "autoOpen": False,
                "welcomeMessage": "Hello! How can I help you today?"
            },
            "content": {
                "title": "AI Assistant",
                "placeholder": "Type your message..."
            }
        }


# Global widget service instance
widget_service = WidgetService()

async def get_widget_service() -> WidgetService:
    """Dependency to get widget service."""
    return widget_service