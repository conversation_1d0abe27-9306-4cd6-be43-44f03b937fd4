"""
Service for managing embeddable chat widgets.
"""
from typing import List, Optional, Dict, Any
import uuid
import secrets
from datetime import datetime
from urllib.parse import urlparse

from app.models.schemas import (
    ChatWidget, ChatWidgetCreate, ChatWidgetUpdate,
    PaginatedResponse
)
from app.database import get_supabase_client
import structlog

logger = structlog.get_logger()


class WidgetService:
    """Service for managing embeddable chat widgets."""
    
    # ... (create_widget, get_widget, get_widget_by_api_key, etc. remain the same) ...
    # PASTE THE EXISTING METHODS FROM YOUR FILE HERE, up to _domain_matches
    async def create_widget(self, widget_data: ChatWidgetCreate) -> ChatWidget:
        """Create a new chat widget."""
        try:
            supabase_client = await get_supabase_client()
            
            widget_dict = widget_data.model_dump()
            widget_dict["id"] = str(uuid.uuid4())
            widget_dict["chatbot_id"] = str(widget_data.chatbot_id)
            widget_dict["api_key"] = self._generate_api_key()
            widget_dict["created_at"] = datetime.utcnow().isoformat()
            widget_dict["updated_at"] = datetime.utcnow().isoformat()
            
            response = supabase_client.table("chat_widgets").insert(widget_dict).execute()
            
            if not response.data:
                raise Exception("Failed to create widget")
            
            return ChatWidget(**response.data[0])
            
        except Exception as e:
            logger.error(f"Error creating widget: {e}")
            raise
    
    async def get_widget(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID) -> Optional[ChatWidget]:
        """Get a specific widget by ID."""
        try:
            supabase_client = await get_supabase_client()
            
            response = supabase_client.table("chat_widgets").select("*").eq("id", str(widget_id)).eq("chatbot_id", str(chatbot_id)).execute()
            
            if response.data:
                return ChatWidget(**response.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting widget: {e}")
            raise
    
    async def get_widget_by_api_key(self, api_key: str) -> Optional[ChatWidget]:
        """Get a widget by its API key."""
        try:
            supabase_client = await get_supabase_client()
            
            response = supabase_client.table("chat_widgets").select("*").eq("api_key", api_key).eq("is_active", True).execute()
            
            if response.data:
                return ChatWidget(**response.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting widget by API key: {e}")
            raise
    
    async def get_chatbot_widgets(
        self, 
        chatbot_id: uuid.UUID, 
        skip: int = 0, 
        limit: int = 10,
        active_only: bool = True
    ) -> PaginatedResponse:
        """Get all widgets for a chatbot with pagination."""
        try:
            supabase_client = await get_supabase_client()
            
            # Build query
            query = supabase_client.table("chat_widgets").select("*", count="exact").eq("chatbot_id", str(chatbot_id))
            
            if active_only:
                query = query.eq("is_active", True)
            
            # Get total count
            count_response = query.execute()
            total = len(count_response.data) if count_response.data else 0
            
            # Get paginated results
            query = query.range(skip, skip + limit - 1).order("created_at", desc=True)
            response = query.execute()
            
            widgets = [ChatWidget(**widget) for widget in response.data] if response.data else []
            
            return PaginatedResponse(
                items=widgets,
                total=total,
                page=skip // limit + 1,
                per_page=limit,
                pages=(total + limit - 1) // limit
            )
            
        except Exception as e:
            logger.error(f"Error getting chatbot widgets: {e}")
            raise
    
    async def update_widget(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID, widget_data: ChatWidgetUpdate) -> Optional[ChatWidget]:
        """Update an existing widget."""
        try:
            supabase_client = await get_supabase_client()
            
            # Only update fields that are provided
            update_dict = {k: v for k, v in widget_data.model_dump().items() if v is not None}
            
            if not update_dict:
                # If no fields to update, return current widget
                return await self.get_widget(widget_id, chatbot_id)
            
            update_dict["updated_at"] = datetime.utcnow().isoformat()
            
            response = supabase_client.table("chat_widgets").update(update_dict).eq("id", str(widget_id)).eq("chatbot_id", str(chatbot_id)).execute()
            
            if response.data:
                return ChatWidget(**response.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error updating widget: {e}")
            raise
    
    async def delete_widget(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID) -> bool:
        """Delete a widget (soft delete by setting is_active=False)."""
        try:
            supabase_client = await get_supabase_client()
            
            response = supabase_client.table("chat_widgets").update({
                "is_active": False,
                "updated_at": datetime.utcnow().isoformat()
            }).eq("id", str(widget_id)).eq("chatbot_id", str(chatbot_id)).execute()
            
            return bool(response.data)
            
        except Exception as e:
            logger.error(f"Error deleting widget: {e}")
            raise
    
    async def regenerate_api_key(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID) -> Optional[ChatWidget]:
        """Regenerate API key for a widget."""
        try:
            supabase_client = await get_supabase_client()
            
            new_api_key = self._generate_api_key()
            
            response = supabase_client.table("chat_widgets").update({
                "api_key": new_api_key,
                "updated_at": datetime.utcnow().isoformat()
            }).eq("id", str(widget_id)).eq("chatbot_id", str(chatbot_id)).execute()
            
            if response.data:
                return ChatWidget(**response.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error regenerating API key: {e}")
            raise
    
    async def validate_widget_access(self, api_key: str, origin: Optional[str]) -> tuple[bool, Optional[ChatWidget]]:
        """Validate if a request from a specific origin is allowed for the widget."""
        try:
            widget = await self.get_widget_by_api_key(api_key)
            if not widget:
                return False, None
            
            if not widget.is_active:
                return False, widget
            
            if not widget.allowed_domains:
                return True, widget
            
            if not origin:
                return False, widget

            try:
                parsed_origin = urlparse(origin)
                origin_domain = parsed_origin.netloc.lower()
            except Exception:
                return False, widget
            
            allowed = any(
                self._domain_matches(origin_domain, allowed_domain.strip().lower())
                for allowed_domain in widget.allowed_domains
            )
            
            return allowed, widget
            
        except Exception as e:
            logger.error(f"Error validating widget access: {e}")
            return False, None

    def _domain_matches(self, origin_domain: str, allowed_domain: str) -> bool:
        """Check if origin domain matches allowed domain (supports wildcards)."""
        if allowed_domain == "*":
            return True
        if origin_domain == allowed_domain:
            return True
        if allowed_domain.startswith("*."):
            base_domain = allowed_domain[2:]
            return origin_domain.endswith(f".{base_domain}") or origin_domain == base_domain
        return False
    
    def _generate_api_key(self) -> str:
        """Generate a secure API key for the widget."""
        return f"widget_{secrets.token_urlsafe(32)}"

    def _generate_api_key(self) -> str:
        """Generate a secure API key for the widget."""
        return f"widget_{secrets.token_urlsafe(32)}"

    async def generate_widget_script(self, api_key: str) -> Optional[str]:
        """Generates an enhanced JavaScript widget with premium UI."""
        widget = await self.get_widget_by_api_key(api_key)
        if not widget:
            return "console.error('AI Chat Widget: Invalid API key or widget is inactive.');"

        # Get widget configuration
        config = widget.widget_config or {}
        appearance = config.get("appearance", {})
        behavior = config.get("behavior", {})

        # Extract configuration values
        primary_color = appearance.get("primaryColor", "#3B82F6")
        position = appearance.get("position", "bottom-right")
        welcome_message = behavior.get("welcomeMessage", "Hello! How can I help you today?")
        auto_open = behavior.get("autoOpen", False)
        auto_open_delay = behavior.get("autoOpenDelay", 5000)

        base_url = "http://localhost:8000"

        return f"""
(function() {{
    'use strict';

    // Prevent multiple instances
    if (window.AIChatWidget) return;

    // Widget configuration
    const config = {{
        apiKey: '{widget.api_key}',
        baseUrl: '{base_url}',
        primaryColor: '{primary_color}',
        position: '{position}',
        welcomeMessage: '{welcome_message}',
        autoOpen: {str(auto_open).lower()},
        autoOpenDelay: {auto_open_delay}
    }};

    // Create widget container
    const widgetContainer = document.createElement('div');
    widgetContainer.id = 'ai-chat-widget-container';
    widgetContainer.innerHTML = `
        <style>
            #ai-chat-widget-container {{
                position: fixed;
                z-index: 999999;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            }}

            .ai-chat-widget-button {{
                position: fixed;
                width: 60px;
                height: 60px;
                border-radius: 50%;
                background: {primary_color};
                border: none;
                cursor: pointer;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000000;
            }}

            .ai-chat-widget-button:hover {{
                transform: scale(1.1);
                box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
            }}

            .ai-chat-widget-button.bottom-right {{
                bottom: 20px;
                right: 20px;
            }}

            .ai-chat-widget-button.bottom-left {{
                bottom: 20px;
                left: 20px;
            }}

            .ai-chat-widget-button.top-right {{
                top: 20px;
                right: 20px;
            }}

            .ai-chat-widget-button.top-left {{
                top: 20px;
                left: 20px;
            }}

            .ai-chat-widget-icon {{
                width: 24px;
                height: 24px;
                fill: white;
                transition: transform 0.3s ease;
            }}

            .ai-chat-widget-button.open .ai-chat-widget-icon {{
                transform: rotate(45deg);
            }}

            .ai-chat-widget-iframe {{
                position: fixed;
                width: 100vw;
                height: 100vh;
                max-width: 400px;
                max-height: 600px;
                border: none;
                border-radius: 16px;
                box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
                background: white;
                transform: scale(0.8) translateY(20px);
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                z-index: 999999;
            }}

            .ai-chat-widget-iframe.open {{
                transform: scale(1) translateY(0);
                opacity: 1;
                visibility: visible;
            }}

            .ai-chat-widget-iframe.bottom-right {{
                bottom: 90px;
                right: 20px;
            }}

            .ai-chat-widget-iframe.bottom-left {{
                bottom: 90px;
                left: 20px;
            }}

            .ai-chat-widget-iframe.top-right {{
                top: 90px;
                right: 20px;
            }}

            .ai-chat-widget-iframe.top-left {{
                top: 90px;
                left: 20px;
            }}

            @media (max-width: 480px) {{
                .ai-chat-widget-iframe {{
                    width: calc(100vw - 20px);
                    height: calc(100vh - 100px);
                    max-width: none;
                    max-height: none;
                    border-radius: 12px;
                    bottom: 90px !important;
                    right: 10px !important;
                    left: 10px !important;
                    top: auto !important;
                }}

                .ai-chat-widget-button {{
                    bottom: 20px !important;
                    right: 20px !important;
                    top: auto !important;
                    left: auto !important;
                }}
            }}

            .ai-chat-widget-notification {{
                position: absolute;
                top: -8px;
                right: -8px;
                width: 20px;
                height: 20px;
                background: #EF4444;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: bold;
                color: white;
                animation: pulse 2s infinite;
            }}

            @keyframes pulse {{
                0% {{ transform: scale(1); }}
                50% {{ transform: scale(1.1); }}
                100% {{ transform: scale(1); }}
            }}

            .ai-chat-widget-overlay {{
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.3);
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                z-index: 999998;
            }}

            .ai-chat-widget-overlay.open {{
                opacity: 1;
                visibility: visible;
            }}

            @media (min-width: 481px) {{
                .ai-chat-widget-overlay {{
                    display: none;
                }}
            }}
        </style>

        <div class="ai-chat-widget-overlay" id="ai-chat-overlay"></div>

        <button class="ai-chat-widget-button {position}" id="ai-chat-button" aria-label="Open chat">
            <svg class="ai-chat-widget-icon" viewBox="0 0 24 24">
                <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
            </svg>
        </button>

        <iframe
            class="ai-chat-widget-iframe {position}"
            id="ai-chat-iframe"
            src="{base_url}/widget/{widget.api_key}"
            title="AI Chat Assistant"
            allow="microphone; camera"
        ></iframe>
    `;

    // Widget functionality
    class AIChatWidget {{
        constructor() {{
            this.isOpen = false;
            this.button = document.getElementById('ai-chat-button');
            this.iframe = document.getElementById('ai-chat-iframe');
            this.overlay = document.getElementById('ai-chat-overlay');
            this.hasNewMessage = false;

            this.initializeEventListeners();
            this.setupAutoOpen();
            this.setupMessageListener();
        }}

        initializeEventListeners() {{
            this.button.addEventListener('click', () => this.toggle());
            this.overlay.addEventListener('click', () => this.close());

            // Close on escape key
            document.addEventListener('keydown', (e) => {{
                if (e.key === 'Escape' && this.isOpen) {{
                    this.close();
                }}
            }});
        }}

        setupAutoOpen() {{
            if (config.autoOpen) {{
                setTimeout(() => {{
                    if (!this.isOpen) {{
                        this.open();
                    }}
                }}, config.autoOpenDelay);
            }}
        }}

        setupMessageListener() {{
            window.addEventListener('message', (event) => {{
                if (event.origin !== config.baseUrl) return;

                if (event.data.type === 'ai-chat-new-message') {{
                    this.showNotification();
                }}

                if (event.data.type === 'ai-chat-resize') {{
                    this.handleResize(event.data.height);
                }}
            }});
        }}

        toggle() {{
            if (this.isOpen) {{
                this.close();
            }} else {{
                this.open();
            }}
        }}

        open() {{
            this.isOpen = true;
            this.button.classList.add('open');
            this.iframe.classList.add('open');
            this.overlay.classList.add('open');
            this.hideNotification();

            // Focus iframe for accessibility
            setTimeout(() => {{
                this.iframe.focus();
            }}, 300);
        }}

        close() {{
            this.isOpen = false;
            this.button.classList.remove('open');
            this.iframe.classList.remove('open');
            this.overlay.classList.remove('open');
        }}

        showNotification() {{
            if (!this.isOpen && !this.hasNewMessage) {{
                this.hasNewMessage = true;
                const notification = document.createElement('div');
                notification.className = 'ai-chat-widget-notification';
                notification.textContent = '1';
                this.button.appendChild(notification);
            }}
        }}

        hideNotification() {{
            const notification = this.button.querySelector('.ai-chat-widget-notification');
            if (notification) {{
                notification.remove();
                this.hasNewMessage = false;
            }}
        }}

        handleResize(height) {{
            if (height && window.innerWidth > 480) {{
                this.iframe.style.height = Math.min(height, 600) + 'px';
            }}
        }}
    }}

    // Initialize widget
    function initWidget() {{
        document.body.appendChild(widgetContainer);
        window.AIChatWidget = new AIChatWidget();
    }}

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {{
        document.addEventListener('DOMContentLoaded', initWidget);
    }} else {{
        initWidget();
    }}

}})();
"""

    async def get_widget_embed_code(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID) -> Dict[str, str]:
        """Generate enhanced embed codes for the widget."""
        try:
            widget = await self.get_widget(widget_id, chatbot_id)
            if not widget:
                raise ValueError(f"Widget {widget_id} not found")

            base_url = "http://localhost:8000"
            config = widget.widget_config or {}
            appearance = config.get("appearance", {})

            # Extract configuration
            primary_color = appearance.get("primaryColor", "#3B82F6")
            position = appearance.get("position", "bottom-right")

            # Premium Script Tag (Recommended)
            script_url = f"{base_url}/api/v1/widgets/script.js?key={widget.api_key}"
            premium_script = f"""<!-- AI Chatbot Widget - Premium (Recommended) -->
<script src="{script_url}" async defer></script>"""

            # Inline Script Version
            inline_script = f"""<!-- AI Chatbot Widget - Inline Script -->
<script>
(function(){{
    const script = document.createElement('script');
    script.src = '{script_url}';
    script.async = true;
    script.defer = true;
    document.head.appendChild(script);
}})();
</script>"""

            # React Component
            react_component = f"""// React Component for AI Chat Widget
import React, {{ useEffect }} from 'react';

const AIChatWidget = () => {{
    useEffect(() => {{
        // Load widget script
        const script = document.createElement('script');
        script.src = '{script_url}';
        script.async = true;
        script.defer = true;
        document.head.appendChild(script);

        return () => {{
            // Cleanup
            const existingScript = document.querySelector(`script[src="${{script.src}}"]`);
            if (existingScript) {{
                existingScript.remove();
            }}

            // Remove widget container
            const widgetContainer = document.getElementById('ai-chat-widget-container');
            if (widgetContainer) {{
                widgetContainer.remove();
            }}

            // Clear global reference
            if (window.AIChatWidget) {{
                delete window.AIChatWidget;
            }}
        }};
    }}, []);

    return null; // Widget renders itself
}};

export default AIChatWidget;"""

            # Vue.js Component
            vue_component = f"""<!-- Vue.js Component for AI Chat Widget -->
<template>
  <!-- Widget renders itself via script -->
</template>

<script>
export default {{
  name: 'AIChatWidget',
  mounted() {{
    this.loadWidget();
  }},
  beforeUnmount() {{
    this.cleanupWidget();
  }},
  methods: {{
    loadWidget() {{
      const script = document.createElement('script');
      script.src = '{script_url}';
      script.async = true;
      script.defer = true;
      document.head.appendChild(script);
    }},
    cleanupWidget() {{
      // Remove script
      const existingScript = document.querySelector(`script[src="{script_url}"]`);
      if (existingScript) {{
        existingScript.remove();
      }}

      // Remove widget container
      const widgetContainer = document.getElementById('ai-chat-widget-container');
      if (widgetContainer) {{
        widgetContainer.remove();
      }}

      // Clear global reference
      if (window.AIChatWidget) {{
        delete window.AIChatWidget;
      }}
    }}
  }}
}}
</script>"""

            # Angular Component
            angular_component = f"""// Angular Component for AI Chat Widget
import {{ Component, OnInit, OnDestroy }} from '@angular/core';

@Component({{
  selector: 'app-ai-chat-widget',
  template: '<!-- Widget renders itself via script -->',
}})
export class AiChatWidgetComponent implements OnInit, OnDestroy {{

  ngOnInit(): void {{
    this.loadWidget();
  }}

  ngOnDestroy(): void {{
    this.cleanupWidget();
  }}

  private loadWidget(): void {{
    const script = document.createElement('script');
    script.src = '{script_url}';
    script.async = true;
    script.defer = true;
    document.head.appendChild(script);
  }}

  private cleanupWidget(): void {{
    // Remove script
    const existingScript = document.querySelector(`script[src="{script_url}"]`);
    if (existingScript) {{
      existingScript.remove();
    }}

    // Remove widget container
    const widgetContainer = document.getElementById('ai-chat-widget-container');
    if (widgetContainer) {{
      widgetContainer.remove();
    }}

    // Clear global reference
    if ((window as any).AIChatWidget) {{
      delete (window as any).AIChatWidget;
    }}
  }}
}}"""

            # WordPress Plugin Code
            wordpress_code = f"""<?php
// WordPress AI Chat Widget
// Add this to your theme's functions.php file

function add_ai_chat_widget() {{
    ?>
    <script src="{script_url}" async defer></script>
    <?php
}}
add_action('wp_footer', 'add_ai_chat_widget');

// Or use as a shortcode: [ai_chat_widget]
function ai_chat_widget_shortcode() {{
    return '<script src="{script_url}" async defer></script>';
}}
add_shortcode('ai_chat_widget', 'ai_chat_widget_shortcode');
?>"""

            # Shopify Integration
            shopify_code = f"""<!-- Shopify AI Chat Widget Integration -->
<!-- Add this to your theme.liquid file before the closing </body> tag -->
<script src="{script_url}" async defer></script>

<!-- Or add to a specific template file -->
<!-- For product pages: templates/product.liquid -->
<!-- For collection pages: templates/collection.liquid -->
<!-- For all pages: layout/theme.liquid -->"""

            # Google Tag Manager
            gtm_code = f"""<!-- Google Tag Manager - AI Chat Widget -->
<!-- Add this as a Custom HTML tag in GTM -->
<script>
(function(){{
    const script = document.createElement('script');
    script.src = '{script_url}';
    script.async = true;
    script.defer = true;
    document.head.appendChild(script);
}})();
</script>"""

            # Configuration object for customization
            customization_guide = f"""// Widget Customization Guide
// The widget automatically detects dark mode from user's system preferences
// You can customize the widget appearance by updating the widget configuration

// Available configuration options:
const widgetConfig = {{
    appearance: {{
        primaryColor: '{primary_color}',     // Main brand color
        position: '{position}',              // bottom-right, bottom-left, top-right, top-left
        theme: 'auto',                       // auto, light, dark
        borderRadius: '16px',                // Widget border radius
        zIndex: 999999                       // Z-index for positioning
    }},
    behavior: {{
        autoOpen: false,                     // Auto-open widget on page load
        autoOpenDelay: 5000,                 // Delay before auto-opening (ms)
        welcomeMessage: 'Hello! How can I help you today?',
        enableNotifications: true,           // Show notification badge
        enableSounds: false                  // Enable notification sounds
    }},
    features: {{
        enableTypingIndicator: true,         // Show typing indicator
        enableFileUpload: false,             // Allow file uploads
        enableEmoji: true,                   // Enable emoji picker
        enableMarkdown: true                 // Support markdown in messages
    }}
}};

// To update configuration, use the widget management API
// or update through your dashboard"""

            return {
                "premium_script": premium_script.strip(),
                "inline_script": inline_script.strip(),
                "react_component": react_component.strip(),
                "vue_component": vue_component.strip(),
                "angular_component": angular_component.strip(),
                "wordpress_code": wordpress_code.strip(),
                "shopify_code": shopify_code.strip(),
                "gtm_code": gtm_code.strip(),
                "customization_guide": customization_guide.strip(),
                "widget_url": f"{base_url}/widget/{widget.api_key}",
                "script_url": script_url,
                "configuration": widget.widget_config
            }

        except Exception as e:
            logger.error(f"Error generating embed code: {e}")
            raise

    async def get_widget_analytics(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID, days: int = 30) -> Dict[str, Any]:
        """Get analytics data for a widget."""
        try:
            # This would typically query analytics data from a separate analytics service
            # For now, return mock data structure
            
            analytics = {
                "widget_id": str(widget_id),
                "period_days": days,
                "total_conversations": 0,
                "total_messages": 0,
                "average_session_duration": 0,
                "popular_questions": [],
                "daily_stats": [],
                "domain_stats": {},
                "action_executions": 0
            }
            
            # In a real implementation, you would:
            # 1. Query conversation logs
            # 2. Calculate metrics
            # 3. Return real analytics data
            
            return analytics
            
        except Exception as e:
            logger.error(f"Error getting widget analytics: {e}")
            raise
    
    async def customize_widget_appearance(
        self, 
        widget_id: uuid.UUID, 
        chatbot_id: uuid.UUID, 
        appearance_config: Dict[str, Any]
    ) -> Optional[ChatWidget]:
        """Update widget appearance configuration."""
        try:
            widget = await self.get_widget(widget_id, chatbot_id)
            if not widget:
                return None
            
            # Merge appearance config with existing widget config
            current_config = widget.widget_config.copy()
            current_config.update({
                "appearance": appearance_config,
                "updated_at": datetime.utcnow().isoformat()
            })
            
            update_data = ChatWidgetUpdate(widget_config=current_config)
            return await self.update_widget(widget_id, chatbot_id, update_data)
            
        except Exception as e:
            logger.error(f"Error customizing widget appearance: {e}")
            raise

    def get_default_widget_config(self) -> Dict[str, Any]:
        """Get enhanced default widget configuration."""
        return {
            "appearance": {
                "theme": "auto",  # auto, light, dark
                "primaryColor": "#3B82F6",
                "secondaryColor": "#64748B",
                "position": "bottom-right",  # bottom-right, bottom-left, top-right, top-left
                "width": "400px",
                "height": "600px",
                "borderRadius": "16px",
                "zIndex": 999999,
                "fontFamily": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
            },
            "behavior": {
                "autoOpen": False,
                "autoOpenDelay": 5000,
                "welcomeMessage": "Hello! How can I help you today?",
                "enableNotifications": True,
                "enableSounds": False,
                "closeOnClickOutside": True,
                "showTypingIndicator": True
            },
            "content": {
                "title": "AI Assistant",
                "subtitle": "We typically reply instantly",
                "placeholder": "Type your message...",
                "sendButtonText": "Send",
                "offlineMessage": "We're currently offline. Leave a message and we'll get back to you!"
            },
            "features": {
                "enableTypingIndicator": True,
                "enableFileUpload": False,
                "enableEmoji": True,
                "enableMarkdown": True,
                "enableTimestamps": True,
                "enableMessageHistory": True,
                "maxMessageLength": 1000
            },
            "branding": {
                "showPoweredBy": True,
                "logoUrl": None,
                "companyName": None,
                "customCSS": None
            },
            "accessibility": {
                "enableKeyboardNavigation": True,
                "enableScreenReader": True,
                "highContrast": False,
                "reducedMotion": False
            },
            "analytics": {
                "trackEvents": True,
                "trackUserAgent": False,
                "trackLocation": False
            }
        }


# Global widget service instance
widget_service = WidgetService()

async def get_widget_service() -> WidgetService:
    """Dependency to get widget service."""
    return widget_service